import pandas as pd
import math
import itertools
import matplotlib.pyplot as plt
import numpy as np
from matplotlib.patches import Circle

def load_all_attachments_p3():
    """加载并处理问题三所需的全部附件数据。"""
    # 附件1: 收集点坐标
    df_coords = pd.read_csv('附件1.xlsx - 附件130个垃圾分类收集点坐标及总垃圾量.csv')
    customer_nodes = {}
    for _, row in df_coords.iterrows():
        customer_nodes[row['收集点编号']] = {'x': row['x坐标'], 'y': row['y坐标']}
    
    # 附件4: 候选中转站信息
    df_hubs = pd.read_csv('附件4.xlsx - 附件4中转站候选位置及参数.csv')
    hub_nodes = {}
    for _, row in df_hubs.iterrows():
        hub_id = row['中转站编号']
        hub_nodes[hub_id] = {
            'x': row['x坐标'], 'y': row['y坐标'],
            'T_annual': row['建设成本Tj(万元)'] * 10000 / 10, # 年化成本
            'S': [row['厨余垃圾最大存储量Sk(吨)'], row['可回收物最大存储量Sk(吨)'], 
                  row['有害垃圾最大存储量Sk(吨)'], row['其他垃圾最大存储量Sk(吨)']],
            'time_window': [row['允许车辆停靠时间窗口'].split('-')[0], row['允许车辆停靠时间窗口'].split('-')[1]]
        }

    # 合并所有节点
    all_nodes = {0: {'x': 0, 'y': 0}, **customer_nodes, **hub_nodes}
    
    # 附件2: 车辆参数
    df_vehicles = pd.read_csv('附件2.xlsx - 附件24类运输车辆参数.csv')
    vehicle_params = {}
    for _, row in df_vehicles.iterrows():
        k = int(row['车辆类型'].replace('车辆类型', '')) - 1
        vehicle_params[k] = {
            'Q': row['载重Qk (吨)'], 
            'V': row['容积Vk (m³)'], 
            'C': row['单位距离运输成本Ck (元/km)'],
            'E': row['碳排放系数Ek (kg/km)']  # 添加碳排放系数
        }
    
    # 附件3: 垃圾需求量
    df_waste = pd.read_csv('附件3.xlsx - 附件330个收集点的4类垃圾量分布.csv')
    demands_w = {k: {i: 0 for i in customer_nodes} for k in range(4)}
    waste_cols = ['厨余垃圾重量(w1)', '可回收物重量(w2)', '有害垃圾重量(w3)', '其他垃圾重量(w4)']
    for _, row in df_waste.iterrows():
        customer_id = row['收集点编号']
        for k in range(4):
            demands_w[k][customer_id] = row[waste_cols[k]]
            
    print("✅ 全部附件(1,2,3,4)数据加载并整合成功。")
    return all_nodes, customer_nodes, hub_nodes, vehicle_params, demands_w
def get_distance_matrix(nodes):
    """构建考虑附件5规则的非对称距离矩阵。"""
    matrix = {i: {j: 0 for j in nodes} for i in nodes}
    for i in nodes:
        for j in nodes:
            # 默认使用欧几里得距离
            dist = math.sqrt((nodes[i]['x'] - nodes[j]['x'])**2 + (nodes[i]['y'] - nodes[j]['y'])**2)
            matrix[i][j] = round(dist)

    # 应用附件5的非对称规则
    # 注意：此处假设禁行时段的影响是固定的，采用绕行距离
    # 实际应用中可以根据时间动态判断
    rules = {(4, 31): 18, (31, 4): 15,
             (27, 28): 14, (28, 27): 18,
             (23, 0): 45, (0, 23): 40,
             (9, 16): 8, (16, 9): 10}
    for (i, j), dist in rules.items():
        if i in matrix and j in matrix[i]:
            matrix[i][j] = dist
            
    print("✅ 非对称距离矩阵已根据附件5规则构建。")
    return matrix
def clarke_wright_solver_p3(depot, customer_list, demands_w_k, Q_k, distance_matrix):
    """适用于问题三的VRP求解器，使用预计算的距离矩阵。"""
    if not customer_list:
        return [], 0.0

    savings = []
    for i in customer_list:
        for j in customer_list:
            if i < j:
                s_ij = distance_matrix[depot][i] + distance_matrix[depot][j] - distance_matrix[i][j]
                savings.append({'i': i, 'j': j, 'value': s_ij})
    
    savings.sort(key=lambda x: x['value'], reverse=True)
    routes = {node: [depot, node, depot] for node in customer_list}
    
    for s in savings:
        i, j = s['i'], s['j']
        route_i, route_j = routes.get(i), routes.get(j)
        if route_i and route_j and route_i != route_j and route_i[-2] == i and route_j[1] == j:
            merged_nodes = route_i[1:-1] + route_j[1:-1]
            if sum(demands_w_k.get(n, 0) for n in merged_nodes) <= Q_k:
                new_route = route_i[:-1] + route_j[1:]
                for node in new_route[1:-1]:
                    routes[node] = new_route

    final_routes = list(set(map(tuple, routes.values())))
    total_dist = sum(sum(distance_matrix[r[i]][r[i+1]] for i in range(len(r)-1)) for r in final_routes)
    return final_routes, total_dist

def calculate_carbon_emissions(routes, distance_matrix, vehicle_params, k):
    """计算给定路径的碳排放量"""
    total_emissions = 0
    for route in routes:
        route_distance = sum(distance_matrix[route[i]][route[i+1]] for i in range(len(route)-1))
        total_emissions += route_distance * vehicle_params[k]['E']
    return total_emissions

def solve_problem_three(carbon_cost_factor=0.5):
    """主函数：执行完整两阶段算法，考虑碳排放"""
    # 1. 加载数据和距离矩阵
    all_nodes, customer_nodes, hub_nodes, vehicle_params, demands_w = load_all_attachments_p3()
    distance_matrix = get_distance_matrix(all_nodes)
    
    hub_ids = list(hub_nodes.keys())
    best_solution = {'cost': float('inf'), 'hubs': None, 'routes': None}

    # 2. 阶段一：遍历所有中转站组合
    print("\n🚚 开始遍历31种中转站组合进行评估...")
    for i in range(1, len(hub_ids) + 1):
        for open_hubs_tuple in itertools.combinations(hub_ids, i):
            open_hubs = list(open_hubs_tuple)
            
            # --- 分配客户到最近的开放中转站 ---
            assignment = {}
            hub_loads = {hub_id: {k: 0 for k in range(4)} for hub_id in open_hubs}
            valid_assignment = True
            for cust_id in customer_nodes:
                nearest_hub = min(open_hubs, key=lambda h: distance_matrix[cust_id][h])
                assignment[cust_id] = nearest_hub
                for k in range(4):
                    hub_loads[nearest_hub][k] += demands_w[k][cust_id]
            
            # --- 检查中转站容量 ---
            for hub_id in open_hubs:
                for k in range(4):
                    if hub_loads[hub_id][k] > hub_nodes[hub_id]['S'][k]:
                        valid_assignment = False
                        break
                if not valid_assignment: break
            
            if not valid_assignment:
                continue # 此组合无效，跳过

            # 3. 阶段二：若组合有效，则进行路径优化
            fixed_cost = sum(hub_nodes[h]['T_annual'] for h in open_hubs)
            routing_cost = 0
            carbon_emissions = 0
            all_routes_for_config = {}

            for hub_id in open_hubs:
                all_routes_for_config[hub_id] = {}
                customers_for_this_hub = [cid for cid, hid in assignment.items() if hid == hub_id]
                for k in range(4):
                    demands_k_for_hub = {cid: demands_w[k][cid] for cid in customers_for_this_hub if demands_w[k][cid] > 0}
                    if not demands_k_for_hub: continue
                    
                    routes, dist = clarke_wright_solver_p3(hub_id, list(demands_k_for_hub.keys()), demands_k_for_hub, vehicle_params[k]['Q'], distance_matrix)
                    routing_cost += dist * vehicle_params[k]['C']
                    
                    # 计算碳排放
                    emissions = calculate_carbon_emissions(routes, distance_matrix, vehicle_params, k)
                    carbon_emissions += emissions
                    
                    all_routes_for_config[hub_id][k] = routes
            
            # 总成本 = 固定成本 + 运输成本 + 碳排放成本
            total_cost = fixed_cost + routing_cost + carbon_emissions * carbon_cost_factor
            
            # 4. 更新最优解
            if total_cost < best_solution['cost']:
                best_solution['cost'] = total_cost
                best_solution['hubs'] = open_hubs
                best_solution['routes'] = all_routes_for_config
                best_solution['fixed_cost'] = fixed_cost
                best_solution['routing_cost'] = routing_cost
                best_solution['carbon_emissions'] = carbon_emissions
                best_solution['carbon_cost'] = carbon_emissions * carbon_cost_factor
                best_solution['assignment'] = assignment

    # 5. 打印最终报告
    print("\n✅ 全部分析完成！最优方案如下：")
    print("="*50)
    print(f"⭐ 最优中转站选址: {best_solution['hubs']}")
    print(f"⭐ 最低年化总成本: {best_solution['cost']:.2f} 元")
    print(f"   - 中转站年化建设成本: {best_solution['fixed_cost']:.2f} 元")
    print(f"   - 年化运输总成本: {best_solution['routing_cost']:.2f} 元")
    print(f"   - 碳排放量: {best_solution['carbon_emissions']:.2f} kg")
    print(f"   - 碳排放成本: {best_solution['carbon_cost']:.2f} 元")
    print("\n最优路径规划详情:")
    waste_names = {0: "厨余", 1: "可回收", 2: "有害", 3: "其他"}
    for hub_id, waste_routes in best_solution['routes'].items():
        print(f"\n  --- 中转站 {hub_id} 的运输任务 ---")
        for k, routes in waste_routes.items():
            print(f"    - {waste_names[k]}垃圾 (需 {len(routes)} 辆车):")
            for r_idx, route in enumerate(routes):
                print(f"      车{r_idx+1}: {' → '.join(map(str, route))}")
    print("="*50)
    
    # 6. 可视化结果
    visualize_solution(all_nodes, customer_nodes, hub_nodes, best_solution)
    
    return best_solution

def visualize_solution(all_nodes, customer_nodes, hub_nodes, solution):
    """可视化最优解决方案"""
    plt.figure(figsize=(12, 10))
    
    # 绘制收集点
    for cust_id, node in customer_nodes.items():
        hub_id = solution['assignment'][cust_id]
        color = plt.cm.tab10(solution['hubs'].index(hub_id) % 10)
        plt.scatter(node['x'], node['y'], c=[color], s=50, alpha=0.7)
        plt.text(node['x']+0.5, node['y']+0.5, str(cust_id), fontsize=8)
    
    # 绘制中转站
    for hub_id in solution['hubs']:
        node = hub_nodes[hub_id]
        plt.scatter(node['x'], node['y'], c='red', s=200, marker='s')
        plt.text(node['x']+0.5, node['y']+0.5, f'H{hub_id}', fontsize=12, fontweight='bold')
        
        # 绘制服务范围圆圈
        radius = max(math.sqrt((node['x']-all_nodes[cust_id]['x'])**2 + 
                              (node['y']-all_nodes[cust_id]['y'])**2)
                    for cust_id, assigned_hub in solution['assignment'].items() 
                    if assigned_hub == hub_id)
        circle = Circle((node['x'], node['y']), radius, fill=False, 
                        linestyle='--', color='red', alpha=0.3)
        plt.gca().add_patch(circle)
    
    # 绘制处理厂
    plt.scatter(0, 0, c='black', s=300, marker='*')
    plt.text(0.5, 0.5, 'Factory', fontsize=12, fontweight='bold')
    
    # 绘制路径
    waste_colors = {0: 'green', 1: 'blue', 2: 'purple', 3: 'orange'}
    for hub_id, waste_routes in solution['routes'].items():
        for k, routes in waste_routes.items():
            for route in routes:
                for i in range(len(route)-1):
                    start = all_nodes[route[i]]
                    end = all_nodes[route[i+1]]
                    plt.plot([start['x'], end['x']], [start['y'], end['y']], 
                             c=waste_colors[k], linewidth=1, alpha=0.6)
    
    # 添加图例和标题
    waste_names = {0: "厨余垃圾", 1: "可回收物", 2: "有害垃圾", 3: "其他垃圾"}
    legend_elements = [plt.Line2D([0], [0], color=waste_colors[k], lw=2, label=waste_names[k]) 
                      for k in waste_colors]
    legend_elements.append(plt.Line2D([0], [0], marker='s', color='w', 
                                     markerfacecolor='red', markersize=10, label='中转站'))
    legend_elements.append(plt.Line2D([0], [0], marker='*', color='w', 
                                     markerfacecolor='black', markersize=15, label='处理厂'))
    
    plt.legend(handles=legend_elements, loc='upper right')
    plt.title('最优中转站选址与路径规划方案', fontsize=16)
    plt.xlabel('X坐标', fontsize=12)
    plt.ylabel('Y坐标', fontsize=12)
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.tight_layout()
    plt.savefig('optimal_solution_p3.png', dpi=300)
    plt.show()

# 运行问题三的求解
if __name__ == "__main__":
    solve_problem_three(carbon_cost_factor=0.5)  # 碳排放成本系数可调整
