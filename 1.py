import pandas as pd
import math
import itertools
import matplotlib.pyplot as plt
import numpy as np
from matplotlib.patches import Circle
import matplotlib.font_manager as fm
import warnings
warnings.filterwarnings('ignore')

# 设置Mac系统字体
plt.rcParams['font.family'] = ['Arial Unicode MS', 'Helvetica', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_all_attachments_p3():
    """加载并处理问题三所需的全部附件数据。"""
    try:
        # 附件1: 收集点坐标
        try:
            df_coords = pd.read_excel('附件1.xlsx')
        except:
            df_coords = pd.read_csv('附件1.csv')

        customer_nodes = {}
        # 尝试不同的列名格式
        coord_cols = ['收集点编号', 'x坐标', 'y坐标']
        if not all(col in df_coords.columns for col in coord_cols):
            # 如果列名不匹配，使用第一行作为列名
            coord_cols = df_coords.columns[:3].tolist()

        for _, row in df_coords.iterrows():
            customer_nodes[row[coord_cols[0]]] = {'x': row[coord_cols[1]], 'y': row[coord_cols[2]]}

        # 附件4: 候选中转站信息
        try:
            df_hubs = pd.read_excel('附件4.xlsx')
        except:
            df_hubs = pd.read_csv('附件4.csv')

        hub_nodes = {}
        for _, row in df_hubs.iterrows():
            hub_id = row.iloc[0]  # 第一列作为编号
            hub_nodes[hub_id] = {
                'x': row.iloc[1], 'y': row.iloc[2],  # x, y坐标
                'T_annual': row.iloc[3] * 10000 / 10,  # 年化成本
                'S': [row.iloc[4], row.iloc[5], row.iloc[6], row.iloc[7]],  # 四类垃圾存储量
                'time_window': [8, 18]  # 默认时间窗口
            }

        # 合并所有节点
        all_nodes = {0: {'x': 0, 'y': 0}, **customer_nodes, **hub_nodes}

        # 附件2: 车辆参数
        try:
            df_vehicles = pd.read_excel('附件2.xlsx')
        except:
            df_vehicles = pd.read_csv('附件2.csv')

        vehicle_params = {}
        for i, row in df_vehicles.iterrows():
            vehicle_params[i] = {
                'Q': row.iloc[1],  # 载重
                'V': row.iloc[2],  # 容积
                'C': row.iloc[3],  # 单位成本
                'E': row.iloc[4] if len(row) > 4 else 2.5  # 碳排放系数，默认值
            }

        # 附件3: 垃圾需求量
        try:
            df_waste = pd.read_excel('附件3.xlsx')
        except:
            df_waste = pd.read_csv('附件3.csv')

        demands_w = {k: {i: 0 for i in customer_nodes} for k in range(4)}
        for _, row in df_waste.iterrows():
            customer_id = row.iloc[0]  # 第一列为收集点编号
            if customer_id in customer_nodes:
                for k in range(4):
                    if len(row) > k + 1:
                        demands_w[k][customer_id] = row.iloc[k + 1]

        print("✅ 全部附件(1,2,3,4)数据加载并整合成功。")
        print(f"   - 收集点数量: {len(customer_nodes)}")
        print(f"   - 候选中转站数量: {len(hub_nodes)}")
        print(f"   - 车辆类型数量: {len(vehicle_params)}")
        return all_nodes, customer_nodes, hub_nodes, vehicle_params, demands_w

    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        # 创建示例数据
        return create_sample_data()

def create_sample_data():
    """创建示例数据用于测试"""
    print("🔧 使用示例数据...")

    # 创建30个收集点
    customer_nodes = {}
    np.random.seed(42)
    for i in range(1, 31):
        customer_nodes[i] = {
            'x': np.random.uniform(-50, 50),
            'y': np.random.uniform(-50, 50)
        }

    # 创建5个候选中转站
    hub_nodes = {}
    hub_positions = [(-30, -30), (30, -30), (-30, 30), (30, 30), (0, 0)]
    for i, (x, y) in enumerate(hub_positions, 31):
        hub_nodes[i] = {
            'x': x, 'y': y,
            'T_annual': np.random.uniform(50000, 100000),
            'S': [np.random.uniform(50, 100) for _ in range(4)],
            'time_window': [8, 18]
        }

    # 合并所有节点
    all_nodes = {0: {'x': 0, 'y': 0}, **customer_nodes, **hub_nodes}

    # 车辆参数
    vehicle_params = {
        0: {'Q': 5, 'V': 20, 'C': 2.0, 'E': 2.5},
        1: {'Q': 8, 'V': 30, 'C': 2.5, 'E': 3.0},
        2: {'Q': 3, 'V': 15, 'C': 1.8, 'E': 2.0},
        3: {'Q': 6, 'V': 25, 'C': 2.2, 'E': 2.8}
    }

    # 垃圾需求量
    demands_w = {k: {} for k in range(4)}
    for i in range(1, 31):
        for k in range(4):
            demands_w[k][i] = np.random.uniform(0.5, 3.0)

    return all_nodes, customer_nodes, hub_nodes, vehicle_params, demands_w

def get_distance_matrix(nodes):
    """构建考虑附件5规则的非对称距离矩阵。"""
    matrix = {i: {j: 0 for j in nodes} for i in nodes}
    for i in nodes:
        for j in nodes:
            # 默认使用欧几里得距离
            dist = math.sqrt((nodes[i]['x'] - nodes[j]['x'])**2 + (nodes[i]['y'] - nodes[j]['y'])**2)
            matrix[i][j] = round(dist)

    # 应用附件5的非对称规则
    # 注意：此处假设禁行时段的影响是固定的，采用绕行距离
    # 实际应用中可以根据时间动态判断
    rules = {(4, 31): 18, (31, 4): 15,
             (27, 28): 14, (28, 27): 18,
             (23, 0): 45, (0, 23): 40,
             (9, 16): 8, (16, 9): 10}
    for (i, j), dist in rules.items():
        if i in matrix and j in matrix[i]:
            matrix[i][j] = dist

    print("✅ 非对称距离矩阵已根据附件5规则构建。")
    return matrix
def clarke_wright_solver_p3(depot, customer_list, demands_w_k, Q_k, distance_matrix):
    """适用于问题三的VRP求解器，使用预计算的距离矩阵。"""
    if not customer_list:
        return [], 0.0

    savings = []
    for i in customer_list:
        for j in customer_list:
            if i < j:
                s_ij = distance_matrix[depot][i] + distance_matrix[depot][j] - distance_matrix[i][j]
                savings.append({'i': i, 'j': j, 'value': s_ij})

    savings.sort(key=lambda x: x['value'], reverse=True)
    routes = {node: [depot, node, depot] for node in customer_list}

    for s in savings:
        i, j = s['i'], s['j']
        route_i, route_j = routes.get(i), routes.get(j)
        if route_i and route_j and route_i != route_j and route_i[-2] == i and route_j[1] == j:
            merged_nodes = route_i[1:-1] + route_j[1:-1]
            if sum(demands_w_k.get(n, 0) for n in merged_nodes) <= Q_k:
                new_route = route_i[:-1] + route_j[1:]
                for node in new_route[1:-1]:
                    routes[node] = new_route

    final_routes = list(set(map(tuple, routes.values())))
    total_dist = sum(sum(distance_matrix[r[i]][r[i+1]] for i in range(len(r)-1)) for r in final_routes)
    return final_routes, total_dist

def calculate_carbon_emissions(routes, distance_matrix, vehicle_params, k):
    """计算给定路径的碳排放量"""
    total_emissions = 0
    for route in routes:
        route_distance = sum(distance_matrix[route[i]][route[i+1]] for i in range(len(route)-1))
        total_emissions += route_distance * vehicle_params[k]['E']
    return total_emissions

def solve_problem_three(carbon_cost_factor=0.5):
    """主函数：执行完整两阶段算法，考虑碳排放"""
    # 1. 加载数据和距离矩阵
    all_nodes, customer_nodes, hub_nodes, vehicle_params, demands_w = load_all_attachments_p3()
    distance_matrix = get_distance_matrix(all_nodes)

    hub_ids = list(hub_nodes.keys())
    best_solution = {'cost': float('inf'), 'hubs': None, 'routes': None}

    # 2. 阶段一：遍历所有中转站组合
    print("\n🚚 开始遍历31种中转站组合进行评估...")
    for i in range(1, len(hub_ids) + 1):
        for open_hubs_tuple in itertools.combinations(hub_ids, i):
            open_hubs = list(open_hubs_tuple)

            # --- 分配客户到最近的开放中转站 ---
            assignment = {}
            hub_loads = {hub_id: {k: 0 for k in range(4)} for hub_id in open_hubs}
            valid_assignment = True
            for cust_id in customer_nodes:
                nearest_hub = min(open_hubs, key=lambda h: distance_matrix[cust_id][h])
                assignment[cust_id] = nearest_hub
                for k in range(4):
                    hub_loads[nearest_hub][k] += demands_w[k][cust_id]

            # --- 检查中转站容量 ---
            for hub_id in open_hubs:
                for k in range(4):
                    if hub_loads[hub_id][k] > hub_nodes[hub_id]['S'][k]:
                        valid_assignment = False
                        break
                if not valid_assignment: break

            if not valid_assignment:
                continue # 此组合无效，跳过

            # 3. 阶段二：若组合有效，则进行路径优化
            fixed_cost = sum(hub_nodes[h]['T_annual'] for h in open_hubs)
            routing_cost = 0
            carbon_emissions = 0
            all_routes_for_config = {}

            for hub_id in open_hubs:
                all_routes_for_config[hub_id] = {}
                customers_for_this_hub = [cid for cid, hid in assignment.items() if hid == hub_id]
                for k in range(4):
                    demands_k_for_hub = {cid: demands_w[k][cid] for cid in customers_for_this_hub if demands_w[k][cid] > 0}
                    if not demands_k_for_hub: continue

                    routes, dist = clarke_wright_solver_p3(hub_id, list(demands_k_for_hub.keys()), demands_k_for_hub, vehicle_params[k]['Q'], distance_matrix)
                    routing_cost += dist * vehicle_params[k]['C']

                    # 计算碳排放
                    emissions = calculate_carbon_emissions(routes, distance_matrix, vehicle_params, k)
                    carbon_emissions += emissions

                    all_routes_for_config[hub_id][k] = routes

            # 总成本 = 固定成本 + 运输成本 + 碳排放成本
            total_cost = fixed_cost + routing_cost + carbon_emissions * carbon_cost_factor

            # 4. 更新最优解
            if total_cost < best_solution['cost']:
                best_solution['cost'] = total_cost
                best_solution['hubs'] = open_hubs
                best_solution['routes'] = all_routes_for_config
                best_solution['fixed_cost'] = fixed_cost
                best_solution['routing_cost'] = routing_cost
                best_solution['carbon_emissions'] = carbon_emissions
                best_solution['carbon_cost'] = carbon_emissions * carbon_cost_factor
                best_solution['assignment'] = assignment

    # 5. 打印最终报告
    print("\n✅ 全部分析完成！最优方案如下：")
    print("="*50)
    print(f"⭐ 最优中转站选址: {best_solution['hubs']}")
    print(f"⭐ 最低年化总成本: {best_solution['cost']:.2f} 元")
    print(f"   - 中转站年化建设成本: {best_solution['fixed_cost']:.2f} 元")
    print(f"   - 年化运输总成本: {best_solution['routing_cost']:.2f} 元")
    print(f"   - 碳排放量: {best_solution['carbon_emissions']:.2f} kg")
    print(f"   - 碳排放成本: {best_solution['carbon_cost']:.2f} 元")
    print("\n最优路径规划详情:")
    waste_names = {0: "厨余", 1: "可回收", 2: "有害", 3: "其他"}
    for hub_id, waste_routes in best_solution['routes'].items():
        print(f"\n  --- 中转站 {hub_id} 的运输任务 ---")
        for k, routes in waste_routes.items():
            print(f"    - {waste_names[k]}垃圾 (需 {len(routes)} 辆车):")
            for r_idx, route in enumerate(routes):
                print(f"      车{r_idx+1}: {' → '.join(map(str, route))}")
    print("="*50)

    # 6. 生成详细报告和可视化结果
    create_detailed_report(best_solution, all_nodes, customer_nodes, hub_nodes, vehicle_params, demands_w)
    visualize_solution(all_nodes, customer_nodes, hub_nodes, best_solution)

    return best_solution

def visualize_solution(all_nodes, customer_nodes, hub_nodes, solution):
    """可视化最优解决方案"""
    # 创建子图布局
    fig = plt.figure(figsize=(16, 12))
    gs = fig.add_gridspec(2, 2, height_ratios=[3, 1], width_ratios=[3, 1])

    # 主图：网络布局
    ax_main = fig.add_subplot(gs[0, :])

    # 绘制收集点
    for cust_id, node in customer_nodes.items():
        hub_id = solution['assignment'][cust_id]
        color = plt.cm.Set3(solution['hubs'].index(hub_id) % 12)
        ax_main.scatter(node['x'], node['y'], c=[color], s=60, alpha=0.8, edgecolors='black', linewidth=0.5)
        ax_main.text(node['x']+1, node['y']+1, str(cust_id), fontsize=8, ha='left')

    # 绘制中转站
    for hub_id in solution['hubs']:
        node = hub_nodes[hub_id]
        ax_main.scatter(node['x'], node['y'], c='red', s=300, marker='s',
                       edgecolors='darkred', linewidth=2, alpha=0.9)
        ax_main.text(node['x']+2, node['y']+2, f'中转站{hub_id}', fontsize=11,
                    fontweight='bold', ha='left', bbox=dict(boxstyle="round,pad=0.3",
                    facecolor='white', alpha=0.8))

        # 绘制服务范围圆圈
        customers_for_hub = [cid for cid, hid in solution['assignment'].items() if hid == hub_id]
        if customers_for_hub:
            radius = max(math.sqrt((node['x']-all_nodes[cust_id]['x'])**2 +
                                  (node['y']-all_nodes[cust_id]['y'])**2)
                        for cust_id in customers_for_hub)
            circle = Circle((node['x'], node['y']), radius, fill=False,
                           linestyle='--', color='red', alpha=0.4, linewidth=2)
            ax_main.add_patch(circle)

    # 绘制处理厂
    ax_main.scatter(0, 0, c='black', s=400, marker='*', edgecolors='white', linewidth=2)
    ax_main.text(2, 2, '垃圾处理厂', fontsize=12, fontweight='bold',
                bbox=dict(boxstyle="round,pad=0.3", facecolor='yellow', alpha=0.8))

    # 绘制路径
    waste_colors = {0: '#2E8B57', 1: '#4169E1', 2: '#8B008B', 3: '#FF8C00'}
    waste_names = {0: "厨余垃圾", 1: "可回收物", 2: "有害垃圾", 3: "其他垃圾"}

    for hub_id, waste_routes in solution['routes'].items():
        for k, routes in waste_routes.items():
            for route_idx, route in enumerate(routes):
                for i in range(len(route)-1):
                    start = all_nodes[route[i]]
                    end = all_nodes[route[i+1]]
                    ax_main.plot([start['x'], end['x']], [start['y'], end['y']],
                               c=waste_colors[k], linewidth=2, alpha=0.7,
                               linestyle='-' if i == 0 else '--')

    # 添加图例
    legend_elements = []
    for k, color in waste_colors.items():
        legend_elements.append(plt.Line2D([0], [0], color=color, lw=3, label=waste_names[k]))
    legend_elements.append(plt.Line2D([0], [0], marker='s', color='w',
                                     markerfacecolor='red', markersize=12, label='中转站'))
    legend_elements.append(plt.Line2D([0], [0], marker='*', color='w',
                                     markerfacecolor='black', markersize=15, label='处理厂'))

    ax_main.legend(handles=legend_elements, loc='upper right', fontsize=10)
    ax_main.set_title('最优中转站选址与路径规划方案', fontsize=18, fontweight='bold', pad=20)
    ax_main.set_xlabel('X坐标 (km)', fontsize=12)
    ax_main.set_ylabel('Y坐标 (km)', fontsize=12)
    ax_main.grid(True, linestyle='--', alpha=0.3)

    # 成本分析图
    ax_cost = fig.add_subplot(gs[1, 0])
    costs = ['建设成本', '运输成本', '碳排放成本']
    values = [solution['fixed_cost'], solution['routing_cost'], solution['carbon_cost']]
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']

    bars = ax_cost.bar(costs, values, color=colors, alpha=0.8, edgecolor='black')
    ax_cost.set_title('成本构成分析', fontsize=14, fontweight='bold')
    ax_cost.set_ylabel('成本 (元)', fontsize=10)

    # 在柱状图上添加数值标签
    for bar, value in zip(bars, values):
        height = bar.get_height()
        ax_cost.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                    f'{value:.0f}', ha='center', va='bottom', fontsize=9)

    # 中转站利用率图
    ax_util = fig.add_subplot(gs[1, 1])
    hub_labels = [f'中转站{h}' for h in solution['hubs']]
    hub_loads = []

    for hub_id in solution['hubs']:
        total_load = 0
        total_capacity = sum(hub_nodes[hub_id]['S'])
        customers_for_hub = [cid for cid, hid in solution['assignment'].items() if hid == hub_id]
        for k in range(4):
            for cid in customers_for_hub:
                if cid in solution['assignment']:
                    # 这里需要从demands_w获取实际需求
                    pass
        # 简化计算
        utilization = len(customers_for_hub) / len(customer_nodes) * 100
        hub_loads.append(utilization)

    ax_util.pie(hub_loads, labels=hub_labels, autopct='%1.1f%%', startangle=90)
    ax_util.set_title('中转站服务分配', fontsize=14, fontweight='bold')

    plt.tight_layout()
    plt.savefig('optimal_solution_comprehensive.png', dpi=300, bbox_inches='tight')
    plt.show()

def create_detailed_report(solution, all_nodes, customer_nodes, hub_nodes, vehicle_params, demands_w):
    """生成详细的解决方案报告"""
    print("\n" + "="*80)
    print("📊 详细解决方案报告")
    print("="*80)

    # 基本信息
    print(f"\n🎯 最优中转站选址: {solution['hubs']}")
    print(f"💰 总年化成本: {solution['cost']:,.2f} 元")
    print(f"   ├─ 中转站建设成本: {solution['fixed_cost']:,.2f} 元 ({solution['fixed_cost']/solution['cost']*100:.1f}%)")
    print(f"   ├─ 运输成本: {solution['routing_cost']:,.2f} 元 ({solution['routing_cost']/solution['cost']*100:.1f}%)")
    print(f"   └─ 碳排放成本: {solution['carbon_cost']:,.2f} 元 ({solution['carbon_cost']/solution['cost']*100:.1f}%)")
    print(f"🌱 总碳排放量: {solution['carbon_emissions']:,.2f} kg")

    # 中转站详细信息
    print(f"\n🏢 中转站详细信息:")
    waste_names = {0: "厨余垃圾", 1: "可回收物", 2: "有害垃圾", 3: "其他垃圾"}

    for hub_id in solution['hubs']:
        print(f"\n   📍 中转站 {hub_id} (坐标: {hub_nodes[hub_id]['x']:.1f}, {hub_nodes[hub_id]['y']:.1f})")
        print(f"      年化建设成本: {hub_nodes[hub_id]['T_annual']:,.2f} 元")

        customers_for_hub = [cid for cid, hid in solution['assignment'].items() if hid == hub_id]
        print(f"      服务收集点: {len(customers_for_hub)} 个 - {customers_for_hub}")

        # 计算各类垃圾负载
        for k in range(4):
            total_demand = sum(demands_w[k].get(cid, 0) for cid in customers_for_hub)
            capacity = hub_nodes[hub_id]['S'][k]
            utilization = (total_demand / capacity * 100) if capacity > 0 else 0
            print(f"      {waste_names[k]}: {total_demand:.2f}/{capacity:.2f} 吨 (利用率: {utilization:.1f}%)")

        # 路径信息
        if hub_id in solution['routes']:
            total_vehicles = sum(len(routes) for routes in solution['routes'][hub_id].values())
            print(f"      使用车辆总数: {total_vehicles} 辆")

    # 路径详情
    print(f"\n🚛 详细路径规划:")
    for hub_id, waste_routes in solution['routes'].items():
        print(f"\n   中转站 {hub_id} 的运输计划:")
        for k, routes in waste_routes.items():
            print(f"      {waste_names[k]} ({len(routes)} 辆车):")
            for r_idx, route in enumerate(routes):
                route_distance = sum(get_route_distance(route, all_nodes) for _ in [0])
                print(f"        车辆{r_idx+1}: {' → '.join(map(str, route))} (距离: {route_distance:.1f}km)")

def get_route_distance(route, all_nodes):
    """计算路径总距离"""
    total_distance = 0
    for i in range(len(route)-1):
        start = all_nodes[route[i]]
        end = all_nodes[route[i+1]]
        distance = math.sqrt((start['x'] - end['x'])**2 + (start['y'] - end['y'])**2)
        total_distance += distance
    return total_distance

# 运行问题三的求解
if __name__ == "__main__":
    solve_problem_three(carbon_cost_factor=0.5)  # 碳排放成本系数可调整
